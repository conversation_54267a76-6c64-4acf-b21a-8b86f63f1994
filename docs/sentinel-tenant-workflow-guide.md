# Sentinel Tenant Workflow Guide

*A comprehensive guide for tenants to understand and integrate with Sentinel AI monitoring service*

## Overview

Sentinel provides AI guardrails as a service, protecting your generative AI applications from security and safety risks. This guide covers the complete tenant journey from setup to ongoing monitoring.

**Read Time**: 2-3 minutes

## What is Sentinel?

Sentinel acts as your AI application's security layer, validating both input prompts and output responses against multiple guardrails to detect:

- **Prompt Injection & Jailbreaks** - Malicious attempts to bypass AI constraints
- **Toxicity & Harmful Content** - Offensive or inappropriate material
- **PII Leakage** - Personal identifiable information exposure
- **Off-Topic Content** - Irrelevant responses to your application's purpose
- **System Prompt Leakage** - Exposure of internal AI instructions

## Tenant Journey Workflow

```mermaid
graph TD
    A[Tenant Application] --> B[API Key Setup]
    B --> C[Integration with /validate]
    C --> D[Send Text + Guardrails]
    D --> E[Sentinel Processing]
    E --> F[Guardrail Results]
    F --> G[Application Decision]
    G --> H[Allow/Block/Modify]
    
    E --> I[Background Logging]
    I --> J[Statistics & Monitoring]
    
    subgraph "Guardrail Processing"
        E --> K[LionGuard]
        E --> L[AWS Bedrock]
        E --> M[Off-Topic]
        E --> N[Prompt Guard]
        K --> F
        L --> F
        M --> F
        N --> F
    end
```

## Authentication & Setup

### 1. API Key Acquisition
- Contact AIGuardian team for API key provisioning
- Receive `x-api-key` for your tenant
- API key links to your tenant ID for proper data isolation

### 2. Environment Configuration
```bash
# Set your API key
SENTINEL_API_KEY=your-api-key-here
SENTINEL_ENDPOINT=https://sentinel.stg.aiguardian.gov.sg/api/v1
```

## Core Integration: /validate Endpoint

### Basic Request Pattern
```bash
curl -X POST "${SENTINEL_ENDPOINT}/validate" \
  -H "x-api-key: ${SENTINEL_API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Your text to validate",
    "guardrails": {
      "lionguard": {},
      "aws": {},
      "off-topic": {}
    }
  }'
```

### Request Components

| Field | Purpose | Required |
|-------|---------|----------|
| `text` | Content to validate (input/output) | ✅ |
| `guardrails` | Specific guardrails to run | ✅ |
| `messages` | Context for off-topic detection | ⚠️ |
| `metadata` | Custom tracking data | ❌ |
| `tags` | Categorization labels | ❌ |

### Response Structure
```json
{
  "status": "completed",
  "request_id": "uuid-here",
  "results": {
    "lionguard": {"score": 0.1},
    "aws": {"score": 0.05},
    "off-topic": {"score": 0.8}
  },
  "time_taken": 1.2
}
```

## Available Guardrails

### Singapore-Contextualized
- **`lionguard`** - Singapore-specific content moderation
- **`lionguard-binary`** - Binary safe/unsafe classification
- **`lionguard-hateful`** - Hate speech detection

### Global Standards
- **`aws`** - AWS Bedrock comprehensive guardrails
- **`aws-pii`** - Personal information detection
- **`aws-prompt-attack`** - Prompt injection detection

### Specialized
- **`off-topic`** - Relevance to application context
- **`jailbreak`** - AI constraint bypass attempts
- **`system-prompt-leakage`** - Internal prompt exposure

## Integration Patterns

### 1. Input Validation (Pre-Processing)
```python
# Before sending to your AI model
def validate_user_input(user_text):
    response = sentinel_client.validate(
        text=user_text,
        guardrails={"lionguard": {}, "jailbreak": {}}
    )
    
    if any(score > 0.8 for score in response.results.values()):
        return "blocked", "Content violates safety guidelines"
    
    return "allowed", user_text
```

### 2. Output Validation (Post-Processing)
```python
# After receiving AI model response
def validate_ai_output(ai_response, user_context):
    response = sentinel_client.validate(
        text=ai_response,
        guardrails={
            "lionguard": {},
            "off-topic": {},
            "system-prompt-leakage": {}
        },
        messages=user_context
    )
    
    return process_guardrail_results(response)
```

### 3. Dual Validation (Comprehensive)
```python
# Validate both input and output
def comprehensive_validation(user_input, ai_output):
    # Input validation
    input_result = validate_input(user_input)
    if input_result.blocked:
        return input_result
    
    # Output validation
    output_result = validate_output(ai_output, user_input)
    return output_result
```

## Monitoring & Analytics

### Automatic Background Logging
Every `/validate` request triggers background logging that captures:

- **Request Details**: Text content, guardrails used, timestamps
- **Results**: Scores, processing time, success/failure status
- **Tenant Context**: API key ID, custom metadata, tags
- **Performance Metrics**: Response times, error rates

**Data Isolation**: Each tenant's data is completely isolated using their API key ID.

### Tenant Admin Monitoring

#### Statistics Dashboard Access
```bash
# Get validation statistics for last hour
curl -X GET "${SENTINEL_ENDPOINT}/statistics?last=3600" \
  -H "x-api-key: ${SENTINEL_API_KEY}"

# Get statistics for specific date range
curl -X GET "${SENTINEL_ENDPOINT}/statistics?start_date=2024-01-01T00:00:00Z&end_date=2024-01-02T00:00:00Z" \
  -H "x-api-key: ${SENTINEL_API_KEY}"
```

#### Key Metrics Available
- **Usage Patterns**: Total validation requests, peak usage times
- **Guardrail Performance**: Average scores by guardrail type
- **System Health**: Success rates, average response times
- **Risk Trends**: High-risk content detection patterns
- **Cost Tracking**: API usage for billing purposes

#### Custom Tracking with Metadata
```python
# Add custom tracking for better monitoring
response = sentinel_client.validate(
    text=user_input,
    guardrails={"lionguard": {}},
    metadata={
        "application": "chatbot-v2",
        "user_type": "premium",
        "feature": "document-analysis"
    },
    tags=["production", "high-priority"]
)
```

**Admin Benefits**:
- Filter analytics by application/feature
- Track usage across different user segments
- Monitor specific deployment environments
- Generate custom reports for compliance

## Error Handling

### Common Error Scenarios

| Status Code | Scenario | Action |
|-------------|----------|---------|
| `401` | Missing/invalid API key | Check API key configuration |
| `400` | Malformed request | Validate request structure |
| `500` | Service unavailable | Implement retry logic |
| `200` | Partial failures | Check `errors` field in response |

### Robust Error Handling
```python
def safe_validate(text, guardrails, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = sentinel_client.validate(text, guardrails)
            
            # Check for partial failures
            if response.errors:
                logger.warning(f"Partial failures: {response.errors}")
            
            return response
            
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                # Fallback: allow content with warning
                logger.error(f"Sentinel unavailable: {e}")
                return default_safe_response()
            
            time.sleep(2 ** attempt)  # Exponential backoff
```

## Best Practices

### Performance Optimization
- **Selective Guardrails**: Only use guardrails relevant to your use case
- **Async Processing**: Consider background validation for non-critical paths
- **Caching**: Cache results for identical content (with TTL)

### Security Considerations
- **API Key Protection**: Store securely, rotate regularly
- **Content Logging**: Be aware that text is logged for monitoring
- **Fallback Strategy**: Define behavior when Sentinel is unavailable

### Integration Tips
- **Gradual Rollout**: Start with logging-only mode before blocking
- **Threshold Tuning**: Adjust score thresholds based on your risk tolerance
- **Context Matters**: Provide message context for off-topic detection
- **Metadata Usage**: Use tags and metadata for better analytics

## Troubleshooting

### Common Issues

**High False Positives**
- Review guardrail selection
- Adjust score thresholds
- Provide better context (messages field)

**Performance Issues**
- Reduce number of guardrails
- Implement request batching
- Add timeout handling

**Integration Errors**
- Verify API key format
- Check request JSON structure
- Validate endpoint URL

### Support
- Technical issues: Contact AIGuardian team
- API documentation: Check `/docs` endpoint
- Status updates: Monitor service health dashboard

---

*This guide provides the foundation for integrating Sentinel into your AI applications. For specific implementation details or advanced use cases, consult the AIGuardian team.*
